export type CellThemeVars = {
    cellFontSize?: string;
    cellLineHeight?: number | string;
    cellVerticalPadding?: string;
    cellHorizontalPadding?: string;
    cellTextColor?: string;
    cellBackground?: string;
    cellBorderColor?: string;
    cellActiveColor?: string;
    cellRequiredColor?: string;
    cellLabelColor?: string;
    cellLabelFontSize?: string;
    cellLabelLineHeight?: number | string;
    cellLabelMarginTop?: string;
    cellValueColor?: string;
    cellIconSize?: string;
    cellRightIconColor?: string;
    cellLargeVerticalPadding?: string;
    cellLargeTitleFontSize?: string;
    cellLargeLabelFontSize?: string;
};
