import { type PropType, type ExtractPropTypes } from 'vue';
export type CircleStartPosition = 'top' | 'right' | 'bottom' | 'left';
export declare const circleProps: {
    text: StringConstructor;
    size: (NumberConstructor | StringConstructor)[];
    fill: {
        type: PropType<string>;
        default: string;
    };
    rate: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    speed: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    color: PropType<string | Record<string, string>>;
    clockwise: {
        type: BooleanConstructor;
        default: true;
    };
    layerColor: StringConstructor;
    currentRate: {
        type: NumberConstructor;
        default: number;
    };
    strokeWidth: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    strokeLinecap: PropType<CanvasLineCap>;
    startPosition: {
        type: PropType<CircleStartPosition>;
        default: CircleStartPosition;
    };
};
export type CircleProps = ExtractPropTypes<typeof circleProps>;
declare const _default: import("vue").DefineComponent<ExtractPropTypes<{
    text: StringConstructor;
    size: (NumberConstructor | StringConstructor)[];
    fill: {
        type: PropType<string>;
        default: string;
    };
    rate: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    speed: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    color: PropType<string | Record<string, string>>;
    clockwise: {
        type: BooleanConstructor;
        default: true;
    };
    layerColor: StringConstructor;
    currentRate: {
        type: NumberConstructor;
        default: number;
    };
    strokeWidth: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    strokeLinecap: PropType<CanvasLineCap>;
    startPosition: {
        type: PropType<CircleStartPosition>;
        default: CircleStartPosition;
    };
}>, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "update:currentRate"[], "update:currentRate", import("vue").PublicProps, Readonly<ExtractPropTypes<{
    text: StringConstructor;
    size: (NumberConstructor | StringConstructor)[];
    fill: {
        type: PropType<string>;
        default: string;
    };
    rate: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    speed: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    color: PropType<string | Record<string, string>>;
    clockwise: {
        type: BooleanConstructor;
        default: true;
    };
    layerColor: StringConstructor;
    currentRate: {
        type: NumberConstructor;
        default: number;
    };
    strokeWidth: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    strokeLinecap: PropType<CanvasLineCap>;
    startPosition: {
        type: PropType<CircleStartPosition>;
        default: CircleStartPosition;
    };
}>> & Readonly<{
    "onUpdate:currentRate"?: ((...args: any[]) => any) | undefined;
}>, {
    fill: string;
    strokeWidth: string | number;
    rate: string | number;
    speed: string | number;
    clockwise: boolean;
    currentRate: number;
    startPosition: CircleStartPosition;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
