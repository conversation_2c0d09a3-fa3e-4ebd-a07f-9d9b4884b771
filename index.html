<html>
  <head>
      <meta charset="utf-8" />
      <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1, minimum-scale=1" />
      <!-- 引入样式文件 -->
      <link rel="stylesheet" href="./vant.css" />

      <link rel="stylesheet" type="text/css" href="./index.css" />
      
      <!-- 必须先引入vue  后使用vant -->
      <script src="./vue.js"></script>
      <script src="./axios.min.js"></script>
      <!-- 引入 Vant 的 JS 文件 -->
      <script src="./vant.js"></script>
      <script src="./config.js"></script>
  </head>
	<body id="body" style="visibility:hidden;">
		<div id="app" :style="{background:colour_1}">
      <div class="container" :style="{background:colour_2}">
        <h1>{{ page_title }}</h1>
        <div class="countdown">
            <div class="countdown-item" :style="{background:colour_3}">
                <div class="countdown-value">{{ days_num }}</div>
                <div class="countdown-label">天</div>
            </div>
            <div class="countdown-item" :style="{background:colour_3}">
                <div class="countdown-value">{{ hours_num }}</div>
                <div class="countdown-label">小时</div>
            </div>
            <div class="countdown-item" :style="{background:colour_3}">
                <div class="countdown-value">{{ minutes_num }}</div>
                <div class="countdown-label">分钟</div>
            </div>
            <div class="countdown-item" :style="{background:colour_3}">
                <div class="countdown-value">{{ seconds_num }}</div>
                <div class="countdown-label">秒</div>
            </div>
        </div>
        <!-- <div class="footer">
            第十五届全国运动会闭幕式时间：2025年11月21日
        </div> -->
      </div>
		</div>
    
	</body>
	<script>
		var vm = new Vue({
			el: '#app',
			data: function(){
				return {
          page_title: '第十五届全国运动会闭幕式倒计时',
          closingCeremonyDate: new Date('2025-11-21T20:00:00'),
          days_num: '00',
          hours_num: '00',
          minutes_num: '00',
          seconds_num: '00',
          colour_1: '#207cca',
          colour_2: 'rgba(0, 0, 0, 0.7)',
          colour_3: 'rgba(255, 255, 255, 0.2)',
        }
			},
      created() {
        this.getTimerSetting()
      },
      mounted() {
      },
      methods: {
        getTimerSetting() {
          axios
          .post(base_api + '/api/timer/v1/timer/get', {})
          .then(res => {
            if(res.data.code === 200) {
              this.page_title = res.data.data.title
              this.closingCeremonyDate = res.data.data.target_time*1000
              this.colour_1 = res.data.data.colour_1
              this.colour_2 = res.data.data.colour_2
              this.colour_3 = res.data.data.colour_3
              // 初始更新
              this.updateCountdown();
              // 每秒更新一次
              setInterval(this.updateCountdown, 1000);
            } else if(res.data.code === 401) {

            } else {
              this.$toast.fail('errCode' + res.data.code);
            }
          }).catch(function (error) { // 请求失败处理
            console.log(error);
          });

        },
        updateCountdown() {
            const now = new Date();
            const diff = this.closingCeremonyDate - now;
            
            // 如果时间已过
            if (diff <= 0) {
                this.days_num = '00'
                this.hours_num = '00'
                this.minutes_num = '00';
                this.seconds_num = '00';
                return;
            }
            
            // 计算天、小时、分钟、秒
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            
            // 更新显示
            this.days_num = days.toString().padStart(2, '0');
            this.hours_num = hours.toString().padStart(2, '0');
            this.minutes_num = minutes.toString().padStart(2, '0');
            this.seconds_num = seconds.toString().padStart(2, '0');
        },
        GetQueryString(name) {
          var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
          var r = window.location.search.substr(1).match(reg);
          if (r != null) return unescape(r[2]); return null;
        },
      },
		})
	</script>
  <script type="text/javascript">
    setTimeout(function(){
        document.getElementById('body').style.visibility = 'visible';
    },150);
  </script>
  <!-- 假设设计稿为1080px，则： -->
  <script>
    function baseSize(){
      var ele = document.documentElement
      ele.style.fontSize = ele.clientWidth / 1080*100 + 'px'
    }
    window.onload=()=>{
      baseSize()
    }
    window.onresize=()=>{baseSize()}
  </script>
</html>